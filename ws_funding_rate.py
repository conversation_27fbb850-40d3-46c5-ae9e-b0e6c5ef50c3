import asyncio
import json

import requests
from websockets import connect


# wss://fstream.binance.com/stream?streams=bnbusdt@aggTrade/btcusdt@markPrice
# !markPrice@arr
# !markPrice@arr@1s

def get_um_symbols():
    url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
    response = requests.get(url)
    data = response.json()
    return [symbol['symbol'] for symbol in data['symbols'] if 'USDT' in symbol['symbol']]


async def subscribe_websocket():
    ws_url = f"wss://fstream.binance.com/ws/!markPrice@arr@1s"

    async with connect(ws_url) as ws:
        while True:
            print(f"WebSocket已连接")
            for message in ws:
                data = json.loads(message)
                print(data)


async def main():
    await subscribe_websocket()


if __name__ == "__main__":
    asyncio.run(main())
