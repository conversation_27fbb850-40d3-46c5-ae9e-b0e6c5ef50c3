import requests
import hmac
import hashlib
import urllib.parse
import time

api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
api_secret = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

proxy = 'geo.iproyal.com:12321'
proxy_auth = 'Wu2j6U99UtUDzJaF:fbTZqjVP5XF23KOL'

proxies = {
   'http': f'socks5h://{proxy_auth}@{proxy}',
   'https': f'socks5h://{proxy_auth}@{proxy}'
}

# 生成签名的函数
def generate_signature(params, secret):
    query_string = urllib.parse.urlencode(params)
    return hmac.new(secret.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()


# 获取U本位交易对的函数
def get_um_symbols():
    url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
    response = requests.get(url, proxies=proxies, verify=False)
    data = response.json()
    return [symbol['symbol'] for symbol in data['symbols'] if 'USDT' in symbol['symbol']]


# 获取单个交易对费率的函数 (已添加签名)
def get_trading_fee(symbol):
    url = 'https://fapi.binance.com/fapi/v1/commissionRate'

    params = {
        'symbol': symbol,
        'timestamp': int(time.time() * 1000)
    }

    # 生成签名
    params['signature'] = generate_signature(params, api_secret)

    headers = {
        'X-MBX-APIKEY': api_key
    }

    response = requests.get(url, headers=headers, params=params, verify=False)
    return response.json()


# 主程序
def main():
    um_symbols = get_um_symbols()
    fee_data = {}

    for symbol in um_symbols:
        try:
            fee_info = get_trading_fee(symbol)
            fee_data[symbol] = fee_info
            print(f"获取{symbol}费率成功")
            print(fee_info)
        except Exception as e:
            print(f"获取{symbol}费率失败: {e}")

    print("\n所有U本位交易对费率获取完成！")
    print(fee_data)


if __name__ == '__main__':
    main()
