2025-06-23 16:50:59 [ERROR] [binance:347] [get_all_funding_rates] 获取所有资金费率信息失败: 请求失败: Cannot connect to host fapi.binance.com:443 ssl:default [Connection reset by peer]
错误类型: APIError
错误信息: 请求失败: Cannot connect to host fapi.binance.com:443 ssl:default [Connection reset by peer]
错误堆栈: Traceback (most recent call last):
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 1263, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/asyncio/base_events.py", line 1159, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/asyncio/base_events.py", line 1192, in _create_connection_transport
    await waiter
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/asyncio/selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [Errno 54] Connection reset by peer

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Develope/python_test/binance.py", line 118, in _fetch_request_api
    async with session.get(url, headers=headers, params=params) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/client.py", line 1482, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 622, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 1189, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 1561, in _create_direct_connection
    raise last_exc
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 1530, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Caskroom/miniconda/base/envs/exchange_trade_python/lib/python3.12/site-packages/aiohttp/connector.py", line 1271, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [Connection reset by peer]

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Develope/python_test/binance.py", line 258, in get_all_funding_rates
    funding_rate_data, funding_info_data, ticker_data, open_interest_data = await asyncio.gather(
                                                                            ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Develope/python_test/binance.py", line 138, in _fetch_request_api
    raise APIError(f"请求失败: {str(e)}")
base.APIError: 请求失败: Cannot connect to host fapi.binance.com:443 ssl:default [Connection reset by peer]

