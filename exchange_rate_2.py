import requests
import hmac
import hashlib
import urllib.parse
import time
import asyncio
import aiohttp

api_key = 'Dj6TX1A9k3jylcNYzIlkUnfb1xKtCIkgZrOp6HeaxaxXTc7EFz5FpO4xcyMtBHiW'
api_secret = 'eZ4RfhVAGj4606CPGEYcO06knDfFynFKsPXkpevALX2jdfpl0gXtyqYiHyHQ0iwh'

proxy = 'geo.iproyal.com:12321'
proxy_auth = 'Wu2j6U99UtUDzJaF:fbTZqjVP5XF23KOL'

# 代理URL
proxy_url = f"socks5h://{proxy_auth}@{proxy}"


# 生成签名的函数
def generate_signature(params, secret):
    query_string = urllib.parse.urlencode(params)
    return hmac.new(secret.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()


# 异步获取U本位交易对的函数
async def get_um_symbols_async():
    url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'

    async with aiohttp.ClientSession() as session:
        async with session.get(url, proxy=proxy_url) as response:
            data = await response.json()
            return [symbol['symbol'] for symbol in data['symbols'] if 'USDT' in symbol['symbol']]


# 异步获取单个交易对费率的函数
async def get_trading_fee_async(session, symbol):
    url = 'https://fapi.binance.com/fapi/v1/commissionRate'

    params = {
        'symbol': symbol,
        'timestamp': int(time.time() * 1000)
    }

    # 生成签名
    params['signature'] = generate_signature(params, api_secret)

    headers = {
        'X-MBX-APIKEY': api_key
    }

    try:
        async with session.get(url, headers=headers, params=params, proxy=proxy_url) as response:
            return symbol, await response.json()
    except Exception as e:
        return symbol, str(e)


# 异步主程序
async def main_async():
    # 异步获取交易对列表
    um_symbols = await get_um_symbols_async()
    fee_data = {}

    # 创建异步会话
    async with aiohttp.ClientSession() as session:
        # 创建所有任务
        tasks = [get_trading_fee_async(session, symbol) for symbol in um_symbols]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

        # 处理结果
        for symbol, result in results:
            if isinstance(result, str):  # 发生异常，result 是错误消息
                print(f"获取{symbol}费率失败: {result}")
            else:
                fee_data[symbol] = result
                print(f"获取{symbol}费率成功")

    print("\n所有U本位交易对费率获取完成！")
    print(fee_data)


if __name__ == '__main__':
    asyncio.run(main_async())
