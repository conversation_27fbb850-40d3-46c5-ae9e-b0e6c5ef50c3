from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
from utils.logger import Logger

class ExchangeError(Exception):
    """交易所相关错误的基类"""
    pass

class APIError(ExchangeError):
    """API 调用错误"""
    pass

class RateLimitError(ExchangeError):
    """触发 API 速率限制错误"""
    pass

class ExchangeBase(ABC):
    """
    交易所基础抽象类
    
    所有具体的交易所实现类都应该继承这个基类，并实现其抽象方法。
    该类提供了基本的错误处理和日志记录功能。
    """
    
    def __init__(self, name: str, api_key: str = "", api_secret: str = ""):
        """
        初始化交易所实例
        
        Args:
            name: 交易所名称
            api_key: API密钥
            api_secret: API密钥对应的密钥
        """
        self.name = name
        self.api_key = api_key
        self.api_secret = api_secret
        self.logger = Logger.get_logger(f"exchange.{name}")
        self.logger.info(f"初始化交易所 {name}")

    @abstractmethod
    async def get_funding_rate(self, symbol: str) -> Dict[str, Any]:
        """
        获取指定交易对的资金费率
        
        Args:
            symbol: 交易对符号，例如 'BTC-USDT'
            
        Returns:
            Dict[str, Any]: 包含费率信息的字典，格式为：
            {
                "symbol": str,
                "funding_rate": float,
                "mark_price": float,
                "next_funding_time": datetime
            }
            
        Raises:
            APIError: API调用失败时抛出
            RateLimitError: 触发API速率限制时抛出
        """
        pass

    @abstractmethod
    async def get_all_funding_rates(self) -> List[Dict[str, Any]]:
        """
        获取所有交易对的资金费率
        
        Returns:
            List[Dict[str, Any]]: 包含所有交易对费率信息的列表，每个元素格式同get_funding_rate的返回值
            
        Raises:
            APIError: API调用失败时抛出
            RateLimitError: 触发API速率限制时抛出
        """
        pass