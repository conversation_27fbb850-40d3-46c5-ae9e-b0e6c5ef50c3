"""
API密钥配置文件
该文件用于从环境变量读取各个交易所的API密钥信息
请勿将此文件提交到版本控制系统
"""

import os

# 交易所API配置（从环境变量读取）
EXCHANGE_API_KEYS = {
    "binance": {
        "api_key": os.getenv("BINANCE_API_KEY", ""),
        "api_secret": os.getenv("BINANCE_API_SECRET", ""),
    },
    "okx": {
        "api_key": os.getenv("OKX_API_KEY", ""),
        "api_secret": os.getenv("OKX_API_SECRET", ""),
        "passphrase": os.getenv("OKX_API_PASSPHRASE", ""),
    },
    "bitget": {
        "api_key": os.getenv("BITGET_API_KEY", ""),
        "api_secret": os.getenv("BITGET_API_SECRET", ""),
    }
} 