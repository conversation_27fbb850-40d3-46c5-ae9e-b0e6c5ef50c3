import time

import requests


# 获取U本位交易对的函数
def get_um_symbols():
    url = 'https://fapi.binance.com/fapi/v1/exchangeInfo'
    response = requests.get(url)
    data = response.json()
    return [symbol['symbol'] for symbol in data['symbols'] if 'USDT' in symbol['symbol']]


# 获取单个交易对费率的函数 (已添加签名)
def get_trading_fee(symbol):
    url = "https://fapi.binance.com/fapi/v1/fundingRate"

    params = {
        'symbol': symbol,
        'timestamp': int(time.time() * 1000)
    }

    # 生成签名
    params['signature'] = generate_signature(params, api_secret)

    headers = {
        'X-MBX-APIKEY': api_key
    }

    response = requests.get(url, headers=headers, params=params)
    return response.json()



params["limit"] = 10000
response = requests.get(url, params)  # 不传symbol参数
all_rates = response.json()  # 返回所有交易对数据 <sup data-citation='{&quot;id&quot;:1,&quot;url&quot;:&quot;合并PLAINTEXT文件 - 创建时间: 20...yright © 2025 Binance.\n&quot;,&quot;title&quot;:&quot;合并PLAINTEXT文件 - 创建时间: 20...yright © 2025 Binance.\n&quot;,&quot;content&quot;:&quot;U本位合约 行情接口 REST API 查询资金费率历史 本页总览 查询资金费率历史 接口描述​ 查询资金费率历史 HTTP请求​ GET fapiv1fundingRate 请求权重​ 和GET fapiv1fundingInfo共享5005minIP 请求参数​ 名称 类型 是否必需 描述 symbol STRING NO 交易对 startTime LONG NO 起始时间 endTime &quot;}'>1</sup>
print(f"共获取{len(all_rates)}个交易对的最新资金费率")
print(all_rates)
